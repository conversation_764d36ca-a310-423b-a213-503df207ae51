<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <LangVersion>latest</LangVersion>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
        <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
        <CompilerGeneratedFilesOutputPath>$(BaseIntermediateOutputPath)\GeneratedFiles</CompilerGeneratedFilesOutputPath>
        
    </PropertyGroup>

    <ItemGroup>
        <AvaloniaResource Include="Assets\**" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia" />
        <PackageReference Include="Avalonia.Themes.Fluent" />
        <PackageReference Include="Avalonia.Fonts.Inter" />
        <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
        <PackageReference Include="Avalonia.Diagnostics">
            <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
            <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
        </PackageReference>
        <PackageReference Include="CommunityToolkit.Mvvm" />
        <PackageReference Include="LiveChartsCore.SkiaSharpView.Avalonia" />
        <PackageReference Include="Material.Avalonia" />
        <PackageReference Include="Material.Avalonia.DataGrid" />
        <PackageReference Include="Material.Avalonia.Dialogs" />
        <PackageReference Include="Material.Icons.Avalonia" />
        <PackageReference Include="Microsoft.CodeAnalysis.CSharp" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
        <PackageReference Include="PanAndZoom" />
        <PackageReference Include="System.Collections.Immutable" />
        <PackageReference Include="System.Reactive" />
        <PackageReference Include="Xaml.Behaviors" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Assets\Images\" />
      <Folder Include="ViewModels\Modules\" />
      <Folder Include="Views\Controls\Editor\" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\KGKMarkingStudio.Generators\KGKMarkingStudio.Generators.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    </ItemGroup>
</Project>
