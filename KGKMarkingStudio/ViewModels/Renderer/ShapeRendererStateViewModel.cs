using KGKMarkingStudio.Model.Renderer;

namespace KGKMarkingStudio.ViewModels.Renderer;

public partial class ShapeRendererStateViewModel : ViewModelBase
{
    [AutoNotify] private double _panX;  //X方向平移大小
    [AutoNotify] private double _panY;
    [AutoNotify] private double _zoomX; //X方向缩放大小
    [AutoNotify] private double _zoomY;
    [AutoNotify] private ShapeStateFlags _drawShapeState;
    [AutoNotify] private IImageCache? _imageCache;
}