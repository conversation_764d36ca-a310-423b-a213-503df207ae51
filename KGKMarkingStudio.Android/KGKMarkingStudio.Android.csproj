<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0-android</TargetFramework>
        <SupportedOSPlatformVersion>21</SupportedOSPlatformVersion>
        <Nullable>enable</Nullable>
        <ApplicationId>com.CompanyName.KGKMarkingStudio</ApplicationId>
        <ApplicationVersion>1</ApplicationVersion>
        <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
        <AndroidPackageFormat>apk</AndroidPackageFormat>
        <AndroidEnableProfiledAot>false</AndroidEnableProfiledAot>
    </PropertyGroup>

    <ItemGroup>
        <AndroidResource Include="Icon.png">
            <Link>Resources\drawable\Icon.png</Link>
        </AndroidResource>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia.Android"/>
        <PackageReference Include="Microsoft.CodeAnalysis.CSharp" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
        <PackageReference Include="PanAndZoom" />
        <PackageReference Include="Xamarin.AndroidX.Core.SplashScreen"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\KGKMarkingStudio\KGKMarkingStudio.csproj"/>
    </ItemGroup>
</Project>
