<Project>
  <!-- https://learn.microsoft.com/en-us/nuget/consume-packages/central-package-management -->
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <!-- Avalonia packages -->
    <!-- Important: keep version in sync! -->
    <PackageVersion Include="Avalonia" Version="11.2.1" />
    <PackageVersion Include="Avalonia.Controls.PanAndZoom" Version="11.2.0" />
    <PackageVersion Include="Avalonia.Themes.Fluent" Version="11.2.1" />
    <PackageVersion Include="Avalonia.Fonts.Inter" Version="11.2.1" />
    <PackageVersion Include="Avalonia.Diagnostics" Version="11.2.1" />
    <PackageVersion Include="Avalonia.Desktop" Version="11.2.1" />
    <PackageVersion Include="Avalonia.iOS" Version="11.2.1" />
    <PackageVersion Include="Avalonia.Browser" Version="11.2.1" />
    <PackageVersion Include="Avalonia.Android" Version="11.2.1" />
    <PackageVersion Include="Avalonia.Xaml.Behaviors" Version="11.2.0.14" />
    <PackageVersion Include="CommunityToolkit.Mvvm" Version="8.3.2" />
    <PackageVersion Include="LiveChartsCore.SkiaSharpView.Avalonia" Version="2.0.0-rc5.4" />
    <PackageVersion Include="Material.Avalonia" Version="3.9.2" />
    <PackageVersion Include="Material.Avalonia.DataGrid" Version="3.9.2" />
    <PackageVersion Include="Material.Avalonia.Dialogs" Version="3.9.2" />
    <PackageVersion Include="Material.Icons.Avalonia" Version="2.2.0" />
    <PackageVersion Include="Microsoft.CodeAnalysis.Analyzers" Version="5.0.0-1.25277.114" />
    <PackageVersion Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="10.0.0-preview.2.25163.2" />
    <PackageVersion Include="Xamarin.AndroidX.Core.SplashScreen" Version="1.0.1.1" />
  </ItemGroup>
</Project>