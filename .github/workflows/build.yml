name: CI

on:
  push:
    branches:
    - master
    - release/*
  pull_request:
    branches:
    - master

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    name: Build ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v1
    - name: Setup .NET Core
      uses: actions/setup-dotnet@v1
    - name: Build Release
      run: dotnet build -c Release
