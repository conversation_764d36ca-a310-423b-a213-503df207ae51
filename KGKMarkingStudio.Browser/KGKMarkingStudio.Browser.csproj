<Project Sdk="Microsoft.NET.Sdk.WebAssembly">
    <PropertyGroup>
        <TargetFramework>net9.0-browser</TargetFramework>
        <OutputType>Exe</OutputType>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia.Browser"/>
        <PackageReference Include="Microsoft.CodeAnalysis.CSharp" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\KGKMarkingStudio\KGKMarkingStudio.csproj"/>
    </ItemGroup>
</Project>
