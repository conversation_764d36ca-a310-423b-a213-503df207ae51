<Window
    x:Class="KGKSmallToolkit.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:KGKSmallToolkit.View"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewmodel="clr-namespace:KGKSmallToolkit.ViewModel"
    Title="KGK Small Toolkit --V1.0.1.7(新线)"
    Width="1024"
    Height="868"
    Background="{DynamicResource MaterialDesignPaper}"
    Closing="Window_Closing"
    FontFamily="{DynamicResource MaterialDesignFont}"
    Icon="/Assets/Images/kgk.ico"
    TextElement.FontSize="13"
    TextElement.FontWeight="Regular"
    TextElement.Foreground="{DynamicResource MaterialDesignBody}"
    TextOptions.TextFormattingMode="Ideal"
    TextOptions.TextRenderingMode="Auto"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Window.DataContext>
        <viewmodel:MainViewModel />
    </Window.DataContext>

    <materialDesign:DialogHost Identifier="mainDialogHost">
        <materialDesign:DialogHost.DialogContent>
            <ContentControl Content="{Binding CurrentDialog}" />
        </materialDesign:DialogHost.DialogContent>

        <Grid>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>


            <!--  Navigation Panle  -->
            <Grid
                x:Name="Nav_grid"
                Grid.Row="1"
                Grid.RowSpan="2"
                Width="80"
                HorizontalAlignment="Left">
                <Border Background="#E0E0E0" CornerRadius="0, 0, 0, 10" />

                <StackPanel VerticalAlignment="Top">
                    <!--  Home  -->
                    <RadioButton
                        Command="{Binding HomeCommand}"
                        IsChecked="True"
                        Style="{StaticResource MenuBtnStyle}">
                        <StackPanel>
                            <materialDesign:PackIcon Kind="Home" Style="{StaticResource IconImage_style}" />
                            <TextBlock Style="{StaticResource Text_style}" Text="主页" />
                        </StackPanel>
                    </RadioButton>
                    <!--  Datas  -->
                    <RadioButton
                        Command="{Binding DatasCommand}"
                        IsChecked="False"
                        Style="{StaticResource MenuBtnStyle}">
                        <StackPanel>
                            <materialDesign:PackIcon Kind="FormatListText" Style="{StaticResource IconImage_style}" />
                            <TextBlock Style="{StaticResource Text_style}" Text="数据" />
                        </StackPanel>
                    </RadioButton>

                </StackPanel>


                <materialDesign:PopupBox
                    Margin="10"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Bottom"
                    PlacementMode="TopAndAlignLeftEdges"
                    ToolTipService.Placement="Right">
                    <StackPanel>
                        <Button Command="{Binding ExitAppCommand}" ToolTip="退出程序">
                            <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                                <materialDesign:PackIcon
                                    Width="32"
                                    Height="32"
                                    VerticalAlignment="Center"
                                    Kind="ExitToApp" />
                                <TextBlock
                                    VerticalAlignment="Center"
                                    Foreground="#FF3D00"
                                    Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                    Text="退出程序" />
                            </StackPanel>
                        </Button>

                    </StackPanel>
                </materialDesign:PopupBox>

            </Grid>

            <!--  Page View  -->
            <ContentControl
                x:Name="Pages"
                Grid.Row="1"
                Grid.Column="1"
                Content="{Binding CurrentView}" />



        </Grid>

    </materialDesign:DialogHost>
</Window>
