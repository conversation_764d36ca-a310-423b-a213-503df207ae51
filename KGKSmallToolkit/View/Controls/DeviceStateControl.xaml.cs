using KGKSmallToolkit.Utilities.Common;
using MaterialDesignThemes.Wpf;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace KGKSmallToolkit.View.Controls
{
    /// <summary>
    /// DeviceConnectStateControl.xaml 的交互逻辑
    /// </summary>
    public partial class DeviceStateControl : UserControl
    {
        public DeviceStateControl()
        {
            InitializeComponent(); 
        }

        public string DeviceName
        {
            get { return (string)GetValue(DeviceNameProperty); }
            set { SetValue(DeviceNameProperty, value); }
        }

        // Using a DependencyProperty as the backing store for DeviceName.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty DeviceNameProperty =
            DependencyProperty.Register("DeviceName", typeof(string), typeof(DeviceStateControl), new PropertyMetadata("未知名称设备"));

        public DeviceState DState
        {
            get { return (DeviceState)GetValue(DStateProperty); }
            set { 
                SetValue(DStateProperty, value); 
            }
        }

        // Using a DependencyProperty as the backing store for IsConnected.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty DStateProperty =
            DependencyProperty.Register("DState", typeof(DeviceState), typeof(DeviceStateControl), new PropertyMetadata(DeviceState.OFF, new PropertyChangedCallback(OnDStateChanged)));

        private static void OnDStateChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            DeviceStateControl obj = d as DeviceStateControl;
            if (obj != null)
            {
                switch ((DeviceState)e.NewValue)
                {
                    case DeviceState.ON:
                        obj.CurrentKind = obj.OnKind;
                        obj.CurrentBrush = obj.OnBrush;
                        break;
                    case DeviceState.OFF:
                        obj.CurrentKind = obj.OffKind;
                        obj.CurrentBrush = obj.OffBrush;
                        break;
                    case DeviceState.UNKNOWN:
                        obj.CurrentKind = obj.UnknownKind;
                        obj.CurrentBrush = obj.UnknownBrush;
                        break;
                }
            }  
        }

        public PackIconKind CurrentKind
        {
            get { return (PackIconKind)GetValue(CurrentKindProperty); }
            set { SetValue(CurrentKindProperty, value); }
        }

        // Using a DependencyProperty as the backing store for CurrentKind.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty CurrentKindProperty =
            DependencyProperty.Register("CurrentKind", typeof(PackIconKind), typeof(DeviceStateControl), new PropertyMetadata(PackIconKind.CheckboxMarkedOutline));




        public PackIconKind OnKind
        {
            get { return (PackIconKind)GetValue(OnKindProperty); }
            set { SetValue(OnKindProperty, value); }
        }

        // Using a DependencyProperty as the backing store for OnKind.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty OnKindProperty =
            DependencyProperty.Register("OnKind", typeof(PackIconKind), typeof(DeviceStateControl), new PropertyMetadata(PackIconKind.None));
         

        public PackIconKind OffKind
        {
            get { return (PackIconKind)GetValue(OffKindProperty); }
            set { SetValue(OffKindProperty, value); }
        }

        // Using a DependencyProperty as the backing store for OffKind.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty OffKindProperty =
            DependencyProperty.Register("OffKind", typeof(PackIconKind), typeof(DeviceStateControl), new PropertyMetadata(PackIconKind.None));



        public PackIconKind UnknownKind
        {
            get { return (PackIconKind)GetValue(UnknownKindProperty); }
            set { SetValue(UnknownKindProperty, value); }
        }

        // Using a DependencyProperty as the backing store for UnknownKind.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty UnknownKindProperty =
            DependencyProperty.Register("UnknownKind", typeof(PackIconKind), typeof(DeviceStateControl), new PropertyMetadata(PackIconKind.None));




        public Brush CurrentBrush
        {
            get { return (Brush)GetValue(CurrentBrushProperty); }
            set { SetValue(CurrentBrushProperty, value); }
        }

        // Using a DependencyProperty as the backing store for CurrentBrush.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty CurrentBrushProperty =
            DependencyProperty.Register("CurrentBrush", typeof(Brush), typeof(DeviceStateControl), new PropertyMetadata(new SolidColorBrush((Color)ColorConverter.ConvertFromString("#747474"))));




        public SolidColorBrush OnBrush
        {
            get { return (SolidColorBrush)GetValue(OnBrushProperty); }
            set { SetValue(OnBrushProperty, value); }
        }

        // Using a DependencyProperty as the backing store for OnBrush.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty OnBrushProperty =
            DependencyProperty.Register("OnBrush", typeof(SolidColorBrush), typeof(DeviceStateControl), new PropertyMetadata(new SolidColorBrush((Color)ColorConverter.ConvertFromString("#008613"))));



        public SolidColorBrush OffBrush
        {
            get { return (SolidColorBrush)GetValue(OffBrushProperty); }
            set { SetValue(OffBrushProperty, value); }
        }

        // Using a DependencyProperty as the backing store for OffBrush.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty OffBrushProperty =
            DependencyProperty.Register("OffBrush", typeof(SolidColorBrush), typeof(DeviceStateControl), new PropertyMetadata(new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E41E00"))));




        public SolidColorBrush UnknownBrush
        {
            get { return (SolidColorBrush)GetValue(UnknownBrushProperty); }
            set { SetValue(UnknownBrushProperty, value); }
        }

        // Using a DependencyProperty as the backing store for UnknownBrush.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty UnknownBrushProperty =
            DependencyProperty.Register("UnknownBrush", typeof(SolidColorBrush), typeof(DeviceStateControl), new PropertyMetadata(new SolidColorBrush((Color)ColorConverter.ConvertFromString("#747474"))));

         

    }
     
}
