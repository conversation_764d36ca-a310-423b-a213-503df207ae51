<UserControl
    x:Class="KGKSmallToolkit.View.CodeScanningControlView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:customer="clr-namespace:KGKSmallToolkit.Utilities"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:localcontrol="clr-namespace:KGKSmallToolkit.View.Controls"
    xmlns:localdll="clr-namespace:KGKSmallToolkit.Utilities.Converters"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:model="clr-namespace:KGKSmallToolkit.Model"
    xmlns:uicontrol="clr-namespace:System.UI.View;assembly=System.UI"
    xmlns:validation="clr-namespace:KGKSmallToolkit.Utilities.Validation"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <localdll:GradeToBrushConverter x:Key="gradeToBrushConverter" />
        <localdll:CodePrintStateToBrushConverter x:Key="stateToBrushConverter" />
        <localdll:CodePrintStateToIconKindConverter x:Key="stateToIconKindConverter" />
        <localdll:CodePrintStateToShowIconKindConverter x:Key="stateToIconShowConverter" />
        <localdll:CodePrintStateToShowProgressBarConverter x:Key="stateToSHowProcessBarConverter" />
        <localdll:CodePrintStateToCheckedConverter x:Key="stateToCheckedConverter" />
        <localdll:BoolReverseConverter x:Key="boolReverserConverter" />
        <localdll:WorkModeToVisibilityConverter x:Key="workMode2VisibilityConverter" />
    </UserControl.Resources>

    <materialDesign:DialogHost Identifier="homeDialogHost">

        <materialDesign:DialogHost.DialogContent>
            <ContentControl Content="{Binding CurrentDialog}" />
        </materialDesign:DialogHost.DialogContent>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="0.7*" />
                <RowDefinition Height="0.3*" />
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0.7*" />
                <ColumnDefinition Width="0.3*" />
            </Grid.ColumnDefinitions>


            <Grid Grid.ColumnSpan="2">
                <!--  State Show  -->
                <StackPanel Margin="20,10,15,0" HorizontalAlignment="Left">
                    <StackPanel Orientation="Horizontal">
                        <localcontrol:DeviceConnectStateControl DeviceName="上位机系统" IsConnected="{Binding IsMesConnected}" />
                        <localcontrol:DeviceConnectStateControl DeviceName="喷码机" IsConnected="{Binding IsCcs3000Connected}" />


                        <Button
                            Margin="30,0,5,0"
                            HorizontalAlignment="Left"
                            Command="{Binding CounterResetCommand}"
                            Style="{StaticResource MaterialDesignIconButton}"
                            ToolTip="Counter Reset">
                            <materialDesign:PackIcon
                                Width="24"
                                Height="24"
                                Kind="Number0BoxOutline" />
                        </Button>

                        <ComboBox
                            Width="128"
                            MinHeight="35"
                            Padding="10,1"
                            customer:ItemsControlHelper.EnumValuesToItemSource="True"
                            IsEnabled="{Binding IsWorking, Converter={StaticResource boolReverserConverter}}"
                            SelectedItem="{Binding CurMode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}" />

                        <Button
                            MinHeight="35"
                            Margin="20,0,0,0"
                            Command="{Binding StartWorkingCommand}"
                            Foreground="White"
                            IsEnabled="{Binding IsWorking, Converter={StaticResource boolReverserConverter}}"
                            Style="{StaticResource MaterialDesignRaisedButton}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon
                                    Width="30"
                                    Height="30"
                                    VerticalAlignment="Center"
                                    Kind="PlayOutline" />
                                <TextBlock
                                    VerticalAlignment="Center"
                                    FontWeight="Bold"
                                    Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                    Text="开始" />
                            </StackPanel>
                        </Button>

                        <Button
                            MinHeight="35"
                            Margin="20,0,0,0"
                            Background="#e65100"
                            BorderBrush="#e65100"
                            Command="{Binding StopWorkingCommand}"
                            Foreground="White"
                            IsEnabled="{Binding IsWorking}"
                            Style="{StaticResource MaterialDesignRaisedButton}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon
                                    Width="30"
                                    Height="30"
                                    VerticalAlignment="Center"
                                    Kind="Stop" />
                                <TextBlock
                                    VerticalAlignment="Center"
                                    FontWeight="Bold"
                                    Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                    Text="停止" />
                            </StackPanel>
                        </Button>


                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Visibility="{Binding ProgressBarVisible}">
                        <ProgressBar
                            Margin="15,10"
                            IsIndeterminate="True"
                            Style="{StaticResource MaterialDesignCircularProgressBar}"
                            Value="0" />
                        <TextBlock
                            VerticalAlignment="Center"
                            Foreground="#E41E00"
                            Style="{StaticResource MaterialDesignTextBlock}"
                            Text="{Binding ProgressBarDescribe}" />
                    </StackPanel>

                </StackPanel>



            </Grid>

            <materialDesign:Card
                Grid.Row="1"
                Grid.ColumnSpan="2"
                Margin="5">
                <WrapPanel Margin="10" IsEnabled="{Binding IsTokenValid}">
                    <ComboBox
                        MinWidth="150"
                        Margin="10"
                        Padding="5"
                        materialDesign:HintAssist.Hint="请选择信息ID"
                        materialDesign:TextFieldAssist.HasClearButton="True"
                        IsEnabled="{Binding IsWorking, Converter={StaticResource boolReverserConverter}}"
                        ItemsSource="{Binding CCSIds}"
                        Style="{StaticResource MaterialDesignFilledComboBox}">
                        <ComboBox.SelectedItem>
                            <Binding Path="SelectCcsId" UpdateSourceTrigger="PropertyChanged">
                                <Binding.ValidationRules>
                                    <validation:NotEmptyValidationRule />
                                </Binding.ValidationRules>
                            </Binding>
                        </ComboBox.SelectedItem>
                    </ComboBox>

                    <CheckBox
                        x:Name="checkIsPrintText"
                        Margin="10"
                        VerticalAlignment="Bottom"
                        Content="喷印明码？"
                        IsChecked="{Binding IsPrintText}"
                        IsEnabled="{Binding IsWorking, Converter={StaticResource boolReverserConverter}}" />

                    <TextBox
                        MinWidth="400"
                        Margin="10,10,10,15"
                        Padding="5"
                        materialDesign:HintAssist.HelperText="示例：7,10 (1~6为第一段, 7~9为第二段，10~结尾为第三段)，以此类推"
                        materialDesign:HintAssist.Hint="请设置明码分行参数..."
                        IsEnabled="{Binding ElementName=checkIsPrintText, Path=IsChecked}"
                        Style="{StaticResource MaterialDesignFilledTextBox}"
                        Text="{Binding TextSplitParameter}" />

                </WrapPanel>
            </materialDesign:Card>



            <Grid Grid.Row="2">

                <materialDesign:Card Margin="5" Visibility="{Binding CurMode, Converter={StaticResource workMode2VisibilityConverter}, ConverterParameter={x:Static model:WorkMode.Debug}}">
                    <TextBlock
                        Margin="10"
                        Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                        Text="调试中..." />
                </materialDesign:Card>

                <!--  Code Scanning  -->
                <materialDesign:Card Margin="5">

                    <Grid
                        Margin="10"
                        Background="White"
                        IsEnabled="{Binding IsTokenValid}">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>


                        <!--  Scan Code  -->
                        <Border
                            Grid.ColumnSpan="4"
                            Margin="5"
                            Padding="5"
                            BorderBrush="LightGray"
                            BorderThickness="1"
                            CornerRadius="3">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <materialDesign:PackIcon
                                    Width="32"
                                    Height="32"
                                    Margin="10,5"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Kind="QrcodeScan" />
                                <TextBlock
                                    Grid.Column="1"
                                    MinWidth="400"
                                    Margin="0,0,5,0"
                                    Padding="5,0,0,0"
                                    VerticalAlignment="Center"
                                    FontSize="20"
                                    Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                    Text="{Binding ScanCode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

                                <TextBlock
                                    Grid.Column="2"
                                    Margin="0,0,5,0"
                                    VerticalAlignment="Center"
                                    Text="{Binding PlanRemaingingCount, StringFormat=剩余：{0:D5}}" />

                            </Grid>
                        </Border>

                        <ScrollViewer
                            Name="listScrollViewer"
                            Grid.Row="4"
                            Grid.ColumnSpan="2"
                            Margin="10"
                            VerticalScrollBarVisibility="Auto">
                            <ItemsControl
                                x:Name="listBoxDatas"
                                Margin="2"
                                ItemsSource="{Binding InkjetCodingBarcodesList}"
                                ScrollViewer.CanContentScroll="True">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border
                                            x:Name="Border"
                                            Padding="8"
                                            BorderBrush="{DynamicResource MaterialDesignDivider}"
                                            BorderThickness="0,0,0,1">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto" />
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="0.3*" />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>

                                                <TextBlock
                                                    MinWidth="50"
                                                    VerticalAlignment="Center"
                                                    FontStyle="Italic"
                                                    Style="{StaticResource MaterialDesignTextBlock}"
                                                    Text="{Binding Id}" />

                                                <TextBlock
                                                    Grid.Column="1"
                                                    Margin="5,0"
                                                    HorizontalAlignment="Left"
                                                    VerticalAlignment="Center"
                                                    FontWeight="Bold"
                                                    Text="{Binding Code}" />

                                                <Border
                                                    Grid.Column="2"
                                                    MinHeight="35"
                                                    Padding="5,3"
                                                    Background="{Binding State, Converter={StaticResource stateToBrushConverter}, UpdateSourceTrigger=PropertyChanged}"
                                                    BorderThickness="0"
                                                    CornerRadius="3">

                                                    <StackPanel Orientation="Horizontal">
                                                        <ProgressBar
                                                            Name="progressBar"
                                                            Margin="15,10"
                                                            IsIndeterminate="True"
                                                            Style="{StaticResource MaterialDesignCircularProgressBar}"
                                                            Visibility="{Binding State, Converter={StaticResource stateToSHowProcessBarConverter}, UpdateSourceTrigger=PropertyChanged}"
                                                            Value="0" />

                                                        <CheckBox IsChecked="{Binding State, Converter={StaticResource stateToCheckedConverter}}" Visibility="Collapsed">
                                                            <i:Interaction.Triggers>
                                                                <i:EventTrigger EventName="Checked">
                                                                    <customer:ScrollToControlAction ScrollViewer="{Binding ElementName=listScrollViewer}" TargetControl="{Binding ElementName=progressBar}" />
                                                                </i:EventTrigger>
                                                            </i:Interaction.Triggers>
                                                        </CheckBox>
                                                        <materialDesign:PackIcon
                                                            Width="24"
                                                            Height="24"
                                                            VerticalAlignment="Center"
                                                            Foreground="White"
                                                            Kind="{Binding State, Converter={StaticResource stateToIconKindConverter}, UpdateSourceTrigger=PropertyChanged}"
                                                            Visibility="{Binding State, Converter={StaticResource stateToIconShowConverter}, UpdateSourceTrigger=PropertyChanged}" />
                                                        <TextBlock
                                                            Margin="5,0"
                                                            HorizontalAlignment="Left"
                                                            VerticalAlignment="Center"
                                                            FontWeight="Bold"
                                                            Foreground="White"
                                                            Text="{Binding Describe}" />
                                                    </StackPanel>
                                                </Border>


                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>

                    </Grid>

                </materialDesign:Card>




            </Grid>
            <!--  Dashboard  -->
            <materialDesign:Card
                Grid.Row="2"
                Grid.Column="1"
                MinWidth="240"
                Margin="5">

                <Grid Margin="10,3">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <Border
                        Margin="5"
                        Background="#2962FF"
                        BorderThickness="0"
                        CornerRadius="3">
                        <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                            <materialDesign:PackIcon
                                Width="32"
                                Height="32"
                                Margin="10,3,0,3"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Foreground="White"
                                Kind="Summation" />
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Foreground="White"
                                Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                Text="Total" />

                            <TextBlock
                                Margin="20,0,0,0"
                                VerticalAlignment="Center"
                                Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                Text="{Binding Total}" />
                        </StackPanel>
                    </Border>

                    <Separator Grid.Row="1" Style="{StaticResource MaterialDesignDarkSeparator}" />

                    <uicontrol:CircularProgressBar
                        Title="{Binding OkCount}"
                        Grid.Row="2"
                        Margin="0,10,0,20"
                        BorderBrush="#008613"
                        BrushStrokeThickness="15"
                        FontSize="14"
                        Foreground="#008613"
                        StrokeThickness="15"
                        Value="{Binding OkPercent}" />

                    <uicontrol:CircularProgressBar
                        Title="{Binding NgCount}"
                        Grid.Row="3"
                        Grid.Column="2"
                        Margin="0,10,0,20"
                        BorderBrush="#E41E00"
                        BrushStrokeThickness="15"
                        FontSize="14"
                        Foreground="#E41E00"
                        StrokeThickness="15"
                        Value="{Binding NGPercent}" />

                    <TextBlock
                        Grid.Row="2"
                        Margin="20,10,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Top"
                        FontWeight="DemiBold"
                        Style="{StaticResource MaterialDesignBody2TextBlock}"
                        Text="OK" />

                    <TextBlock
                        Grid.Row="3"
                        Grid.Column="2"
                        Margin="20,10,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Top"
                        FontWeight="DemiBold"
                        Foreground="#E41E00"
                        Style="{StaticResource MaterialDesignBody2TextBlock}"
                        Text="NG" />

                    <StackPanel
                        Grid.Row="4"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">

                        <localcontrol:DeviceStateControl
                            Grid.Row="4"
                            Margin="5"
                            DState="{Binding InkjetRunningState}"
                            DeviceName="READY"
                            OffBrush="#E41E00"
                            OffKind="AlertCircleOutline"
                            OnBrush="#008613"
                            OnKind="Check"
                            UnknownBrush="#F9A825"
                            UnknownKind="HelpBoxOutline" />
                        <localcontrol:DeviceStateControl
                            Grid.Row="5"
                            Margin="5"
                            DState="{Binding InkBottleState}"
                            DeviceName="墨水"
                            OffBrush="#E41E00"
                            OffKind="AlertCircleOutline"
                            OnBrush="#008613"
                            OnKind="Check"
                            UnknownBrush="#F9A825"
                            UnknownKind="HelpBoxOutline" />
                        <localcontrol:DeviceStateControl
                            Grid.Row="6"
                            Margin="5"
                            DState="{Binding SolventBottleState}"
                            DeviceName="溶剂"
                            OffBrush="#E41E00"
                            OffKind="AlertCircleOutline"
                            OnBrush="#008613"
                            OnKind="Check"
                            UnknownBrush="#F9A825"
                            UnknownKind="HelpBoxOutline" />

                    </StackPanel>


                </Grid>

            </materialDesign:Card>

            <!--  logging  -->
            <materialDesign:Card
                Grid.Row="3"
                Grid.ColumnSpan="2"
                Margin="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <TextBlock
                        Margin="20,5,0,0"
                        FontWeight="Bold"
                        Foreground="{StaticResource PrimaryHueLightForegroundBrush}"
                        Style="{StaticResource MaterialDesignCaptionTextBlock}"
                        Text="日志" />
                    <Button
                        Height="26"
                        Margin="0,5,15,0"
                        Padding="1"
                        HorizontalAlignment="Right"
                        Command="{Binding LogClearCommand}"
                        Foreground="Red"
                        Style="{StaticResource MaterialDesignFlatLightButton}"
                        ToolTip="Clear">
                        <materialDesign:PackIcon
                            Width="22"
                            Height="22"
                            Kind="Clear" />
                    </Button>

                    <ContentControl
                        Grid.Row="1"
                        Margin="5"
                        Content="{Binding Editer}" />
                </Grid>
            </materialDesign:Card>


        </Grid>
    </materialDesign:DialogHost>

</UserControl>
