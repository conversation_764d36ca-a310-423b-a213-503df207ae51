using GalaSoft.MvvmLight.Ioc;
using GalaSoft.MvvmLight.Threading;
using KGKSmallToolkit.ViewModel;
using Microsoft.Practices.ServiceLocation;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.UI.PublicMethod;
using System.Windows;

namespace KGKSmallToolkit
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        public App()
        {
            //ServiceLocator.SetLocatorProvider(() => SimpleIoc.Default);
            //SimpleIoc.Default.Register<CodeSacnningViewModel>();
            //SimpleIoc.Default.Register<DataViewModel>();
            DispatcherHelper.Initialize();
        }

        protected override void OnStartup(StartupEventArgs e)
        { 
            Process instance = RunningInstance();
            if (instance != null)
            {
                HandleRunningInstance(instance);
                Environment.Exit(0);
            }
            //FrameworkElement.FocusVisualStyleProperty.OverrideMetadata(typeof(Control), new FrameworkPropertyMetadata(defaultValue: null));
            base.OnStartup(e);
        }

        private Process RunningInstance()
        {
            Process current = Process.GetCurrentProcess();
            Process[] processes = Process.GetProcessesByName(current.ProcessName);
            foreach (Process process in processes)
            {
                if (process.Id != current.Id)
                {
                    ////保证要打开的进程同已经存在的进程来自同一文件路径
                    //if (Assembly.GetExecutingAssembly().Location.Replace("/", "\\") == current.MainModule.FileName)
                    //{
                    //返回已经存在的进程
                    //UIMessageTip.Show("程序已经在运行中");
                    //MessageTip.Show("当前程序已经在运行中");
                    return process;
                    //}
                }
            }
            return null;
        }

        /// <summary>
        /// 激活Window，并将其窗口放置最前端。
        /// </summary>
        /// <param name="instance"></param>
        private void HandleRunningInstance(Process instance)
        {
            ShowWindowAsync(instance.MainWindowHandle, 1); //调用api函数，正常显示窗口
            SetForegroundWindow(instance.MainWindowHandle); //将窗口放置最前端
        }

        [DllImport("User32.dll")]
        private static extern bool ShowWindowAsync(IntPtr hWnd, int cmdShow);

        [DllImport("User32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);
    }
}
