using KGKSmallToolkit.Utilities.Interface;
using SReaderAPI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;

namespace KGKSmallToolkit.Utilities.Devices.Reader
{
    public class RFIDReader : IDisposable
    {
        private bool disposed;
        private static readonly NLog.Logger Logger = NLog.LogManager.GetCurrentClassLogger();
        private SReader reader = null;
        private int[] stations = { 0, 1 };
         

        public bool Connected { get { return this.reader != null; } }
        public static byte[] CRLF = { 0x0D, 0x0A };
        public string CurStationNo { get; set; }

        private string Content = string.Empty;
         

        public virtual bool Connect(string ip)
        {
            string addr = "tcp://" + ip; 
            try
            {
#if DEBUG
                return true;

#else

                reader = SReader.Create(addr); 
                reader.Connect();
                reader.TagRead += TagRead;
                return true;

#endif

            }
            catch (Exception ex)
            {
                Logger.Error($"尝试连接到RFID读码器失败，信息:{ex.ToString()}");
                return false;
            }
        }

        public void TagRead(Object sender, TagReadDataEventArgs e)
        {
            string epc = e.TagData.EpcString;
            int ant = e.TagData.Ant;
            int rssi = e.TagData.Rssi;

            Content = epc;
            CurStationNo = $"天线{ant}";
            Logger.Info("epc:" + epc + " ant:" + ant + " rssi:" + rssi);
        }

        public virtual void Disconnect()
        {
            if (reader != null)
            {
                reader.ShutDown(); 
                reader = null;  
            }
        }


        public virtual bool RecvMessage(out string result, byte[] endFlag, int timeout = 3000)
        {
#if DEBUG 
            result = "test215461346";
            return true;

#else

            Content = string.Empty;
            while (true)
            {
                Gen2.InventryValue value = new Gen2.InventryValue(4, 0); 
                reader.Inventry(value, null);
                Thread.Sleep(20);
                if (!string.IsNullOrEmpty(Content))
                    break;
            }
            result = Content;
            return true;
#endif
        }
         

        public virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;
            Disconnect();
            disposed = true;
        }
         

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
         

        ~RFIDReader() {
            Dispose(false);
        }

    }
}
