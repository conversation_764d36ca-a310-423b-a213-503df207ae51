using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace KGKSmallToolkit.Utilities.Devices.Reader
{
    public class ReaderBase : DeviceBase, IDisposable
    {
        private bool disposed;
        private static readonly NLog.Logger Logger = NLog.LogManager.GetCurrentClassLogger();
        protected Socket readerSock;
        protected List<byte> tempBuffer = new List<byte>();   //用于粘包处理

        public bool Connected { get { return this.readerSock != null && readerSock.Connected; } }
        public static byte[] CRLF = { 0x0D, 0x0A };

        public bool HasDataRead { get { return this.readerSock.Available > 0; } }

        public virtual bool Connect(string ip, int port)
        {
            //this.readerSock = TryConnect(ip, port);

            readerSock = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
            readerSock.Connect(ip, port); 
            return Connected; 
        }

        public virtual void SendCmd(string cmd, bool isClearBuffer = false)
        {
            if (isClearBuffer)
                ClearBuffer();
            var arrMsg = Encoding.ASCII.GetBytes(cmd);
            this.readerSock.Send(arrMsg);
        }


        public virtual bool RecvMessage(out string result, byte[] endFlag, int timeout = 3000)
        {
            result = string.Empty;
            if (!this.Connected)
                return false;
            //try
            //{
            this.readerSock.ReceiveTimeout = 24 * 60 * 60;  //超时为24小时
            bool recvsuccess = false;
            Stopwatch sw = new Stopwatch();
            sw.Start();
            while (timeout > sw.ElapsedMilliseconds)
            {
                // 定义一个2M的缓存区；
                //int? count = this.readerSock?.Available;
                //if (count > 0)
                //{
                byte[] arrMsgRec = new byte[256];
                int length = 0;
                try {
                    length = this.readerSock.Receive(arrMsgRec, arrMsgRec.Length, 0); // 接收数据，并返回数据的长度； 
                }
                catch (SocketException se)
                {
                    if (se.SocketErrorCode != SocketError.TimedOut)
                    { 
                        throw se;
                    }
                }
                 
                if (length > 0)
                {
                    var content = arrMsgRec.Take(length);
                    Logger.Info($"Recv From Reader [{Encoding.UTF8.GetString(content.ToArray())}]");
                    tempBuffer.AddRange(content);

                    int index = tempBuffer.ToList().IndexOf(endFlag[0]);
                    if (index >= 0)
                    {
                        byte[] tmp = tempBuffer.Take(index).ToArray(); 
                        result = Encoding.ASCII.GetString(tmp);
                        Logger.Info($"Receive Complete DM [{result}]");
                        tempBuffer.RemoveRange(0, index + endFlag.Length); 
                        recvsuccess = true;
                        return recvsuccess;
                    }
                }
                //else
                //    throw new SocketException();
                //}

                Thread.Sleep(2);
            }

            if (timeout < sw.ElapsedMilliseconds)
                recvsuccess = false;
            return recvsuccess;
            //} 
            //catch (Exception ex)
            //{
            //    Logger.Error("Exception on Recieve Reader Message：" + ex.ToString());
            //    return false;
            //}
        }

        public void ClearBuffer()
        {
            int ret = this.readerSock.Available;
            if (ret > 0)
            {
                byte[] arr = new byte[ret];
                this.readerSock.Receive(arr, arr.Length, 0);
            }
            tempBuffer.Clear();
        }


        public virtual void Disconnect()
        {
            if (this.readerSock != null)
            {
                try
                {
                    this.readerSock?.Shutdown(SocketShutdown.Both);
                }
                catch { } 
                this.readerSock?.Dispose();
                this.readerSock = null;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        { 
            if(disposed)
                return; 
            Disconnect(); 
            disposed = true;
        }

        ~ReaderBase() {
            Dispose(false);
        }

    }
}
