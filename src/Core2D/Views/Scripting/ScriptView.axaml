<UserControl x:Class="Core2D.Views.Scripting.ScriptView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vms="using:Core2D.ViewModels.Scripting"
             xmlns:vmd="using:Core2D.ViewModels.Designer"
             mc:Ignorable="d"
             d:DataContext="{x:Static vmd:DesignerContext.Script}"
             d:DesignWidth="1000" d:DesignHeight="600"
             x:DataType="vms:ScriptViewModel" x:CompileBindings="True">
  <UserControl.KeyBindings>
    <KeyBinding Command="{Binding ExecuteRepl}"
                CommandParameter="{Binding #ScriptTextEditor.Text}" 
                Gesture="Ctrl+Enter" />
  </UserControl.KeyBindings>
  <TextBox Name="ScriptTextEditor"
           Margin="0"
           Background="White"
           Foreground="Black"
           AcceptsReturn="True"
           AcceptsTab="True"
           FontFamily="{DynamicResource CodeFontFamily}"
           FontWeight="Normal"
           FontSize="14"
           TextWrapping="NoWrap"
           Text="{Binding Code, Mode=TwoWay}">
    </TextBox>
</UserControl>
