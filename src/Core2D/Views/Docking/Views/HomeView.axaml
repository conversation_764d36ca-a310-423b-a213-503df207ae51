<UserControl x:Class="Core2D.Views.Docking.Views.HomeView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:i="using:Avalonia.Xaml.Interactivity"
             xmlns:idd="using:Avalonia.Xaml.Interactions.DragAndDrop"
             xmlns:dd="using:Core2D.Behaviors.DragAndDrop"
             xmlns:vm="using:Core2D.ViewModels.Docking.Views"
             mc:Ignorable="d"
             d:DesignWidth="800" d:DesignHeight="600"
             x:DataType="vm:HomeViewModel" x:CompileBindings="True">
  <UserControl.Resources>
    <dd:EditorDropHandler x:Key="EditorDropHandler" />
  </UserControl.Resources>
  <i:Interaction.Behaviors>
    <idd:ContextDropBehavior Context="{Binding Context, FallbackValue={x:Null}}" 
                             Handler="{StaticResource EditorDropHandler}" />
  </i:Interaction.Behaviors>
  <ContentControl Content="{Binding ActiveDockable}" />
</UserControl>
