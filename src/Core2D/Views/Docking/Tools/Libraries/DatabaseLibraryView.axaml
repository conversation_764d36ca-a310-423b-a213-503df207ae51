<UserControl x:Class="Core2D.Views.Docking.Tools.Libraries.DatabaseLibraryView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:libraries="using:Core2D.Views.Libraries"
             xmlns:vm="using:Core2D.ViewModels.Docking.Tools.Libraries"
             xmlns:vme="using:Core2D.ViewModels.Editor"
             xmlns:vmd="using:Core2D.ViewModels.Designer"
             mc:Ignorable="d"
             d:DesignWidth="800" d:DesignHeight="600"
             x:DataType="vm:DatabaseLibraryViewModel" x:CompileBindings="True">
  <Panel DataContext="{Binding Context}">
    <Panel x:DataType="vme:ProjectEditorViewModel">
      <libraries:DatabasesView DataContext="{Binding Project}"
                               d:DataContext="{x:Static vmd:DesignerContext.Project}" />
    </Panel>
  </Panel>
</UserControl>
