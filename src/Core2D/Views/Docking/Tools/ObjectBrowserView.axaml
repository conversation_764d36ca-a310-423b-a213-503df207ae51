<UserControl x:Class="Core2D.Views.Docking.Tools.ObjectBrowserView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="using:Core2D.Converters"
             xmlns:vm="using:Core2D.ViewModels.Docking.Tools"
             xmlns:core="using:Core2D.ViewModels"
             xmlns:vmdt="using:Core2D.ViewModels.Data"
             xmlns:vme="using:Core2D.ViewModels.Editor"
             xmlns:vmc="using:Core2D.ViewModels.Containers"
             xmlns:vms="using:Core2D.ViewModels.Shapes"
             xmlns:vmp="using:Core2D.ViewModels.Path"
             xmlns:vmst="using:Core2D.ViewModels.Style"
             xmlns:vmd="using:Core2D.ViewModels.Designer"
             xmlns:c="clr-namespace:Core2D.Controls"
             mc:Ignorable="d"
             d:DesignWidth="800" d:DesignHeight="600"
             x:DataType="vm:ObjectBrowserViewModel" x:CompileBindings="True">
  <UserControl.Resources>
    <ContextMenu x:Key="ExportContextMenu" x:DataType="core:ViewModelBase">
      <MenuItem Header="E_xport" 
                Command="{Binding ExportCommand}"
                CommandParameter="{Binding}" />
    </ContextMenu>
  </UserControl.Resources>
  <Panel DataContext="{Binding Context}">
    <Panel d:DataContext="{x:Static vmd:DesignerContext.Editor}" x:DataType="vme:ProjectEditorViewModel">
      <TreeView x:Name="ProjectTreeView">
        <TreeView.KeyBindings>
          <KeyBinding Command="{Binding ExportCommand}" 
                      CommandParameter="{Binding #ProjectTreeView.SelectedItem}" 
                      Gesture="Ctrl+E"
                      x:DataType="core:ViewModelBase"/>
        </TreeView.KeyBindings>
        <TreeView.Styles>
          <Style Selector="TreeViewItem">
            <Setter Property="IsExpanded" Value="False" />
          </Style>
        </TreeView.Styles>
        <TreeView.DataTemplates>
          <TreeDataTemplate DataType="vmc:LibraryViewModel" 
                            ItemsSource="{Binding Items, FallbackValue={x:Null}}"
                            x:DataType="vmc:LibraryViewModel">
            <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </TreeDataTemplate>
          <TreeDataTemplate DataType="vmc:DocumentContainerViewModel" 
                            ItemsSource="{Binding Pages}"
                            x:DataType="vmc:DocumentContainerViewModel">
            <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </TreeDataTemplate>
          <TreeDataTemplate DataType="vmc:PageContainerViewModel" 
                            ItemsSource="{Binding Layers}"
                            x:DataType="vmc:PageContainerViewModel">
            <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </TreeDataTemplate>
          <TreeDataTemplate DataType="vmc:TemplateContainerViewModel"
                            ItemsSource="{Binding Layers}"
                            x:DataType="vmc:TemplateContainerViewModel">
            <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </TreeDataTemplate>
          <TreeDataTemplate DataType="vmc:LayerContainerViewModel" 
                            ItemsSource="{Binding Shapes}"
                            x:DataType="vmc:LayerContainerViewModel">
            <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </TreeDataTemplate>
          <TreeDataTemplate DataType="vms:GroupShapeViewModel" 
                            ItemsSource="{Binding Shapes}"
                            x:DataType="vms:GroupShapeViewModel">
            <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </TreeDataTemplate>
          <TreeDataTemplate DataType="vms:PathShapeViewModel" 
                            ItemsSource="{Binding Figures, FallbackValue={x:Null}}"
                            x:DataType="vms:PathShapeViewModel">
            <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </TreeDataTemplate>
          <TreeDataTemplate DataType="vmp:PathFigureViewModel" 
                            ItemsSource="{Binding Segments, FallbackValue={x:Null}}"
                            x:DataType="vmp:PathFigureViewModel">
            <c:EditableItem TextBinding="{Binding Mode=OneWay, Converter={x:Static converters:ViewModelToTypeStringConverter.Instance}}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </TreeDataTemplate>
          <DataTemplate DataType="vms:BaseShapeViewModel">
            <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </DataTemplate>
          <DataTemplate DataType="vmst:ShapeStyleViewModel">
            <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </DataTemplate>
          <DataTemplate DataType="vmdt:DatabaseViewModel">
            <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </DataTemplate>
          <DataTemplate DataType="core:ViewModelBase">
            <c:EditableItem TextBinding="{Binding Mode=OneWay, Converter={x:Static converters:ViewModelToTypeStringConverter.Instance}}" 
                            ContextMenu="{StaticResource ExportContextMenu}"/>
          </DataTemplate>
        </TreeView.DataTemplates>
        <TreeViewItem Header="{Binding Name}" 
                      ContextMenu="{StaticResource ExportContextMenu}" 
                      DataContext="{Binding Project}" 
                      IsExpanded="True">
          <TreeViewItem Header="Styles" 
                        ContextMenu="{StaticResource ExportContextMenu}" 
                        DataContext="{Binding StyleLibraries}" 
                        ItemsSource="{Binding}"/>
          <TreeViewItem Header="Groups" 
                        ContextMenu="{StaticResource ExportContextMenu}" 
                        DataContext="{Binding GroupLibraries}" 
                        ItemsSource="{Binding}"/>
          <TreeViewItem Header="Databases" 
                        ContextMenu="{StaticResource ExportContextMenu}" 
                        DataContext="{Binding Databases}" 
                        ItemsSource="{Binding}"/>
          <TreeViewItem Header="Templates" 
                        ContextMenu="{StaticResource ExportContextMenu}" 
                        DataContext="{Binding Templates}" 
                        ItemsSource="{Binding}"/>
          <TreeViewItem Header="Scripts" 
                        ContextMenu="{StaticResource ExportContextMenu}" 
                        DataContext="{Binding Scripts}" 
                        ItemsSource="{Binding}"/>
          <TreeViewItem Header="Documents" 
                        ContextMenu="{StaticResource ExportContextMenu}"
                        DataContext="{Binding Documents}" 
                        ItemsSource="{Binding}"/>
        </TreeViewItem>
      </TreeView>
    </Panel>
  </Panel>
</UserControl>
