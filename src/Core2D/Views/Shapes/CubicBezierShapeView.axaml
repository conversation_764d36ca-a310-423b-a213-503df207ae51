<UserControl x:Class="Core2D.Views.Shapes.CubicBezierShapeView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vms="using:Core2D.ViewModels.Shapes"
             xmlns:vmd="using:Core2D.ViewModels.Designer"
             mc:Ignorable="d"
             d:DataContext="{x:Static vmd:DesignerContext.CubicBezier}"
             d:DesignWidth="250" d:DesignHeight="400"
             x:DataType="vms:CubicBezierShapeViewModel" x:CompileBindings="True">
  <TabControl>
    <TabItem Header="CubicBezier">
      <StackPanel Margin="{DynamicResource ContentMargin}">
        <Label Content="Name" 
               Margin="{DynamicResource TextControlThemePadding}" />
        <TextBox Text="{Binding Name, Mode=TwoWay}" />
        <CheckBox Content="IsStroked" 
                  IsChecked="{Binding IsStroked, Mode=TwoWay}"
                  HorizontalAlignment="Stretch" />
        <CheckBox Content="IsFilled"
                  IsChecked="{Binding IsFilled, Mode=TwoWay}"
                  HorizontalAlignment="Stretch" />
      </StackPanel>
    </TabItem>
    <TabItem Header="Point1">
      <ContentControl Content="{Binding Point1}" />
    </TabItem>
    <TabItem Header="Point2">
      <ContentControl Content="{Binding Point2}" />
    </TabItem>
    <TabItem Header="Point3">
      <ContentControl Content="{Binding Point3}" />
    </TabItem>
    <TabItem Header="Point4">
      <ContentControl Content="{Binding Point4}" />
    </TabItem>
  </TabControl>
</UserControl>
