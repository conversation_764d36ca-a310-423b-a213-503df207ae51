<UserControl x:Class="Core2D.Views.Libraries.TemplatesView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:i="using:Avalonia.Xaml.Interactivity"
             xmlns:idd="using:Avalonia.Xaml.Interactions.DragAndDrop"
             xmlns:dd="using:Core2D.Behaviors.DragAndDrop"
             xmlns:vmc="using:Core2D.ViewModels.Containers"
             xmlns:vmd="using:Core2D.ViewModels.Designer"
             xmlns:c="clr-namespace:Core2D.Controls"
             mc:Ignorable="d"
             d:DataContext="{x:Static vmd:DesignerContext.Project}"
             d:DesignWidth="250" d:DesignHeight="400"
             x:DataType="vmc:ProjectContainerViewModel" x:CompileBindings="True">
  <UserControl.Resources>
    <dd:TemplatesListBoxDropHandler x:Key="TemplatesListBoxDropHandler" />
  </UserControl.Resources>
  <Grid RowDefinitions="Auto,*,12,Auto">
    <StackPanel Grid.Row="0" 
                Orientation="Horizontal" 
                HorizontalAlignment="Right" 
                VerticalAlignment="Center"
                Margin="{DynamicResource ContentMargin}">
      <Button Content="apply"
              Command="{Binding ApplyTemplate}"
              CommandParameter="{Binding CurrentTemplate}" 
              IsVisible="{Binding !!CurrentTemplate}"
              BorderBrush="Transparent"
              Background="Transparent" 
              Padding="12,4,12,4" />
      <Button Content="edit"
              Command="{Binding EditTemplate}"
              CommandParameter="{Binding CurrentTemplate}" 
              IsVisible="{Binding !!CurrentTemplate}"
              BorderBrush="Transparent" 
              Background="Transparent"
              Padding="12,4,12,4" />
      <Button Content="&#727;" 
              Command="{Binding RemoveTemplate}"
              CommandParameter="{Binding CurrentTemplate}" 
              IsVisible="{Binding !!CurrentTemplate}"
              BorderBrush="Transparent" 
              Background="Transparent"
              Padding="12,4,12,4" />
      <Button Content="+" 
              Command="{Binding AddTemplate}" 
              BorderBrush="Transparent"
              Background="Transparent" Padding="12,4,12,4" />
    </StackPanel>
    <ListBox x:Name="TemplatesListBox" 
             ItemsSource="{Binding Templates}"
             SelectedItem="{Binding CurrentTemplate, Mode=TwoWay}" 
             Grid.Row="1">
      <i:Interaction.Behaviors>
        <idd:ContextDropBehavior Context="{Binding FallbackValue={x:Null}}"
                                 Handler="{StaticResource TemplatesListBoxDropHandler}" />
      </i:Interaction.Behaviors>
      <ListBox.KeyBindings>
        <KeyBinding Command="{Binding AddTemplate}" 
                    Gesture="Ctrl+N" />
        <KeyBinding Command="{Binding EditTemplate}"
                    CommandParameter="{Binding CurrentTemplate}" 
                    Gesture="Ctrl+T" />
        <KeyBinding Command="{Binding RemoveTemplate}"
                    CommandParameter="{Binding CurrentTemplate}"
                    Gesture="Delete" />
        <KeyBinding Command="{Binding ExportTemplate}"
                    CommandParameter="{Binding CurrentTemplate}" 
                    Gesture="Ctrl+E" />
      </ListBox.KeyBindings>
      <ListBox.Resources>
        <ContextMenu x:Key="TemplateContainerContextMenu" 
                     x:DataType="vmc:TemplateContainerViewModel">
          <MenuItem Header="_Edit Template" 
                    Command="{Binding EditTemplate}"
                    CommandParameter="{Binding 
                    Mode=OneWay}" />
          <Separator />
          <MenuItem Header="_Delete" 
                    Command="{Binding RemoveTemplate}"
                    CommandParameter="{Binding Mode=OneWay}" />
          <Separator />
          <MenuItem Header="E_xport" 
                    Command="{Binding ExportTemplate}"
                    CommandParameter="{Binding Mode=OneWay}" />
        </ContextMenu>
      </ListBox.Resources>
      <ListBox.DataTemplates>
        <DataTemplate DataType="vmc:TemplateContainerViewModel">
          <c:EditableItem TextBinding="{Binding Name, Mode=TwoWay}" 
                          ContextMenu="{StaticResource TemplateContainerContextMenu}"
                          Padding="0"/>
        </DataTemplate>
      </ListBox.DataTemplates>
    </ListBox>
  </Grid>
</UserControl>
