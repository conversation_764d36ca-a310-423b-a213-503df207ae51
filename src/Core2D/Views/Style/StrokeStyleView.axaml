<UserControl x:Class="Core2D.Views.Style.StrokeStyleView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vms="using:Core2D.ViewModels.Style"
             xmlns:vmd="using:Core2D.ViewModels.Designer"
             mc:Ignorable="d"
             d:DataContext="{x:Static vmd:DesignerContext.StrokeStyle}"
             d:DesignWidth="250" d:DesignHeight="500"
             x:DataType="vms:StrokeStyleViewModel" x:CompileBindings="True">
  <StackPanel Margin="{DynamicResource ContentMargin}">
    <ContentControl Content="{Binding Color}" />
    <Label Content="Thickness" 
           Margin="{DynamicResource TextControlThemePadding}" />
    <TextBox Text="{Binding Thickness, Mode=TwoWay}" />
    <Label Content="Line Cap" 
           Margin="{DynamicResource TextControlThemePadding}" />
    <ComboBox ItemsSource="{x:Static vms:StrokeStyleViewModel.LineCapValues}" 
              SelectedItem="{Binding LineCap, Mode=TwoWay}"
              HorizontalAlignment="Stretch" />
    <Label Content="Dash Offset" 
           Margin="{DynamicResource TextControlThemePadding}" />
    <TextBox Text="{Binding DashOffset, Mode=TwoWay}" />
    <Label Content="Dashes" 
           Margin="{DynamicResource TextControlThemePadding}" />
    <TextBox Text="{Binding Dashes, Mode=TwoWay}" />
    <StackPanel DataContext="{Binding StartArrow}">
      <Grid ColumnDefinitions="40*,6,30*,6,30*">
        <StackPanel Grid.Column="0">
          <Label Content="Start Arrow" 
                 Margin="{DynamicResource TextControlThemePadding}" />
          <ComboBox ItemsSource="{x:Static vms:StrokeStyleViewModel.ArrowTypeValues}" 
                    SelectedItem="{Binding ArrowType, Mode=TwoWay}" 
                    Padding="3"
                    HorizontalAlignment="Stretch" />
        </StackPanel>
        <GridSplitter ResizeDirection="Columns" 
                      ResizeBehavior="PreviousAndNext" 
                      Background="Transparent" 
                      Width="6"
                      Grid.Column="1" />
        <StackPanel Grid.Column="2">
          <Label Content="RadiusX" 
                 Margin="{DynamicResource TextControlThemePadding}" />
          <TextBox Text="{Binding RadiusX, Mode=TwoWay}" />
        </StackPanel>
        <GridSplitter ResizeDirection="Columns"
                      ResizeBehavior="PreviousAndNext"
                      Background="Transparent"
                      Width="6"
                      Grid.Column="3" />
        <StackPanel Grid.Column="4">
          <Label Content="RadiusY" 
                 Margin="{DynamicResource TextControlThemePadding}" />
          <TextBox Text="{Binding RadiusY, Mode=TwoWay}" />
        </StackPanel>
      </Grid>
    </StackPanel>
    <StackPanel DataContext="{Binding EndArrow}">
      <Grid ColumnDefinitions="40*,6,30*,6,30*">
        <StackPanel Grid.Column="0">
          <Label Content="End Arrow" 
                 Margin="{DynamicResource TextControlThemePadding}" />
          <ComboBox ItemsSource="{x:Static vms:StrokeStyleViewModel.ArrowTypeValues}" 
                    SelectedItem="{Binding ArrowType, Mode=TwoWay}"
                    Padding="3" HorizontalAlignment="Stretch" />
        </StackPanel>
        <GridSplitter ResizeDirection="Columns"
                      ResizeBehavior="PreviousAndNext"
                      Background="Transparent"
                      Width="6"
                      Grid.Column="1" />
        <StackPanel Grid.Column="2">
          <Label Content="RadiusX"
                 Margin="{DynamicResource TextControlThemePadding}" />
          <TextBox Text="{Binding RadiusX, Mode=TwoWay}" />
        </StackPanel>
        <GridSplitter ResizeDirection="Columns"
                      ResizeBehavior="PreviousAndNext"
                      Background="Transparent"
                      Width="6"
                      Grid.Column="3" />
        <StackPanel Grid.Column="4">
          <Label Content="RadiusY" 
                 Margin="{DynamicResource TextControlThemePadding}" />
          <TextBox Text="{Binding RadiusY, Mode=TwoWay}" />
        </StackPanel>
      </Grid>
    </StackPanel>
  </StackPanel>
</UserControl>
