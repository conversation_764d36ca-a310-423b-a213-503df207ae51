<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <OutputType>Library</OutputType>
    <IsPackable>True</IsPackable>
    <AnalysisLevel>latest</AnalysisLevel>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GeneratePackageOnBuild>False</GeneratePackageOnBuild>
    <IncludeBuildOutput>False</IncludeBuildOutput>
    <NoPackageAnalysis>True</NoPackageAnalysis>
    <IsRoslynComponent>true</IsRoslynComponent>
    <DisableImplicitNamespaceImports>True</DisableImplicitNamespaceImports>
  </PropertyGroup>

  <PropertyGroup>
    <PackageId>Core2D.Generators</PackageId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.4.0-1.final" PrivateAssets="all" />
    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4-beta1.22362.3" PrivateAssets="all" />
  </ItemGroup>

  <ItemGroup>
    <None Include="$(OutputPath)\$(AssemblyName).dll" Pack="true" PackagePath="analyzers/dotnet/cs" Visible="false" />
  </ItemGroup>

</Project>
